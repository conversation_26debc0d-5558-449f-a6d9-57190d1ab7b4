import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
} from "@mui/material";

interface LinkDialogProps {
  open: boolean;
  linkUrl: string;
  onClose: () => void;
  onUrlChange: (url: string) => void;
  onAdd: () => void;
}

export default function LinkDialog({
  open,
  linkUrl,
  onClose,
  onUrlChange,
  onAdd,
}: LinkDialogProps) {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add Link</DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          margin="dense"
          label="URL"
          type="url"
          fullWidth
          variant="outlined"
          value={linkUrl}
          onChange={(e) => onUrlChange(e.target.value)}
          placeholder="https://example.com"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={onAdd} variant="contained">
          Add Link
        </Button>
      </DialogActions>
    </Dialog>
  );
}
