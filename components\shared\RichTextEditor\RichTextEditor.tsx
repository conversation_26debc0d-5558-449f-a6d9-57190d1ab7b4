"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Link from "@tiptap/extension-link";
import Image from "@tiptap/extension-image";
import TextAlign from "@tiptap/extension-text-align";
import { useCallback, useEffect, useState } from "react";
import { Box, Paper, Typography } from "@mui/material";
import EditorToolbar from "./EditorToolbar";
import LinkDialog from "./LinkDialog";
import ImageDialog from "./ImageDialog";

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  error?: boolean;
  helperText?: string;
  height?: number;
}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = "Start writing...",
  error = false,
  helperText,
  height = 300,
}: RichTextEditorProps) {
  const [linkDialogOpen, setLinkDialogOpen] = useState(false);
  const [imageDialogOpen, setImageDialogOpen] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [selectionUpdate, setSelectionUpdate] = useState(0);

  const editor = useEditor({
    immediatelyRender: false,
    extensions: [
      StarterKit,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "text-blue-600 underline",
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: "max-w-full h-auto rounded",
        },
      }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
    ],
    content: value,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: `prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[${
          height - 100
        }px] p-4`,
        style: `min-height: ${height - 100}px;`,
      },
    },
  });

  // Sync external value
  useEffect(() => {
    if (editor && editor.getHTML() !== value) {
      editor.commands.setContent(value);
    }
  }, [value, editor]);

  // Re-render toolbar when selection changes
  useEffect(() => {
    if (!editor) return;

    editor.on("selectionUpdate", () => {
      setSelectionUpdate((x) => x + 1);
    });
  }, [editor]);

  const addLink = useCallback(() => {
    if (linkUrl) {
      editor
        ?.chain()
        .focus()
        .extendMarkRange("link")
        .setLink({ href: linkUrl })
        .run();
      setLinkUrl("");
      setLinkDialogOpen(false);
    }
  }, [editor, linkUrl]);

  const addImage = useCallback(() => {
    if (imageUrl) {
      editor?.chain().focus().setImage({ src: imageUrl }).run();
      setImageUrl("");
      setImageDialogOpen(false);
    }
  }, [editor, imageUrl]);

  if (!editor) {
    return (
      <Box
        sx={{
          minHeight: height,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Typography color="text.secondary">Loading editor...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Paper
        variant="outlined"
        sx={{
          border: error ? "2px solid #f44336" : "1px solid #ccc",
          borderRadius: 1,
          overflow: "hidden",
        }}
      >
        <EditorToolbar
          editor={editor}
          selectionUpdate={selectionUpdate}
          onLinkClick={() => setLinkDialogOpen(true)}
          onImageClick={() => setImageDialogOpen(true)}
        />

        {/* Editor Content */}
        <Box
          sx={{
            minHeight: height - 48,
            "& .ProseMirror": {
              outline: "none",
              padding: "16px",
              minHeight: height - 80,
              "& p.is-editor-empty:first-of-type::before": {
                content: `"${placeholder}"`,
                float: "left",
                color: "#999",
                pointerEvents: "none",
                height: 0,
              },
              "& h1": {
                fontSize: "2em",
                fontWeight: "bold",
                marginBottom: "0.5em",
              },
              "& h2": {
                fontSize: "1.5em",
                fontWeight: "bold",
                marginBottom: "0.5em",
              },
              "& h3": {
                fontSize: "1.25em",
                fontWeight: "bold",
                marginBottom: "0.5em",
              },
              "& ul": {
                listStyleType: "disc",
                listStylePosition: "outside",
                paddingLeft: "1.5em",
                marginBottom: "1em",
              },
              "& ol": {
                listStyleType: "decimal",
                listStylePosition: "outside",
                paddingLeft: "1.5em",
                marginBottom: "1em",
              },
              "& li": { marginBottom: "0.25em" },
              "& blockquote": {
                borderLeft: "4px solid #ccc",
                paddingLeft: "1em",
                marginLeft: 0,
                fontStyle: "italic",
                color: "#666",
              },
              "& code": {
                backgroundColor: "#f5f5f5",
                padding: "2px 4px",
                borderRadius: "4px",
                fontFamily: "monospace",
              },
              "& pre": {
                backgroundColor: "#f5f5f5",
                padding: "1em",
                borderRadius: "4px",
                overflow: "auto",
                "& code": { backgroundColor: "transparent", padding: 0 },
              },
              "& img": {
                maxWidth: "100%",
                height: "auto",
                borderRadius: "4px",
              },
              "& a": { color: "#1976d2", textDecoration: "underline" },
            },
          }}
        >
          <EditorContent editor={editor} />
        </Box>
      </Paper>

      {helperText && (
        <Typography
          variant="caption"
          sx={{
            color: error ? "#f44336" : "#666",
            mt: 0.5,
            display: "block",
          }}
        >
          {helperText}
        </Typography>
      )}

      <LinkDialog
        open={linkDialogOpen}
        linkUrl={linkUrl}
        onClose={() => setLinkDialogOpen(false)}
        onUrlChange={setLinkUrl}
        onAdd={addLink}
      />

      <ImageDialog
        open={imageDialogOpen}
        imageUrl={imageUrl}
        onClose={() => setImageDialogOpen(false)}
        onUrlChange={setImageUrl}
        onAdd={addImage}
      />
    </Box>
  );
}