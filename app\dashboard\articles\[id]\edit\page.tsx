"use client";

import { useRouter, useParams } from "next/navigation";
import {
  Box,
  Paper,
  Typography,
  Button,
  CircularProgress,
  Alert,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArticleForm from "@/features/articles/ArticleForm";
import { ArticleFormData } from "@/types";
import { useGetArticleQuery, useUpdateArticleMutation } from "@/lib/api/features/articles/articlesApi";

export default function EditArticlePage() {
  const router = useRouter();
  const params = useParams();
  const articleId = parseInt(params.id as string);

  // RTK Query hooks
  const { data: article, isLoading, error } = useGetArticleQuery(articleId);
  const [updateArticle, { isLoading: isSubmitting }] = useUpdateArticleMutation();

  const handleSubmit = async (articleData: ArticleFormData) => {
    try {
      await updateArticle({ id: articleId, data: articleData }).unwrap();
      router.push("/dashboard/articles");
    } catch (error) {
      console.error("Error updating article:", error);
    }
  };

  const handleCancel = () => {
    router.push("/dashboard/articles");
  };

  if (isLoading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load article. Please try again.
      </Alert>
    );
  }

  if (!article) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Article not found.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ mb: 3, display: "flex", alignItems: "center", gap: 2 }}>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleCancel}
        >
          Back to Articles
        </Button>
        <Typography variant="h4" component="h1">
          Edit Article
        </Typography>
      </Box>

      <Paper sx={{ p: 4 }}>
        <ArticleForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          article={article}
          isSubmitting={isSubmitting}
          submitText="Update Article"
          cancelText="Cancel"
        />
      </Paper>
    </Box>
  );
}