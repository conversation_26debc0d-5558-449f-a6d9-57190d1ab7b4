"use client";

import { useState, useMemo } from "react";
import { useSearchParams, usePathname, useRouter } from "next/navigation";
import { Chip, Box, CircularProgress, Alert } from "@mui/material";
import DataTable, { Column, ActionButton } from "@/components/shared/DataTable";
import TagsFilter from "@/features/tags/TagsFilter";
import TagForm from "@/features/tags/TagForm";
import ConfirmDialog from "@/components/shared/ConfirmDialog";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { Tag, TagFormData, TagFilters } from "@/types";
import {
  useGetTagsQuery,
  useCreateTagMutation,
  useUpdateTagMutation,
  useDeleteTagMutation,
} from "@/lib/api/features/tags/tagsApi";
import { Route } from "next";

function TagsPage() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const filters = useMemo((): TagFilters => {
    return {
      search: searchParams.get("search") || undefined,
      offset: parseInt(searchParams.get("offset") || "0"),
      limit: parseInt(searchParams.get("limit") || "10"),
    };
  }, [searchParams]);

  console.log(filters);

  const { data: tagsResponse, isLoading, error } = useGetTagsQuery(filters);
  const [createTag, { isLoading: isCreating }] = useCreateTagMutation();
  const [updateTag, { isLoading: isUpdating }] = useUpdateTagMutation();
  const [deleteTag, { isLoading: isDeleting }] = useDeleteTagMutation();

  const tags = tagsResponse?.data || [];
  const meta = tagsResponse?.meta;

  const [formOpen, setFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedTag, setSelectedTag] = useState<Tag | null>(null);

  if (isLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: 400,
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load tags. Please try again.
      </Alert>
    );
  }

  const tagColumns: Column[] = [
    { accessor: "name", label: "Name", minWidth: 150 },
    { accessor: "slug", label: "Slug", minWidth: 150 },
    { accessor: "createdAt", label: "Created At", minWidth: 150 },
    { accessor: "updatedAt", label: "Updated At", minWidth: 150 },
  ];

  const transformedTags = tags.map((tag) => ({
    ...tag,
    name: tag.name,
  }));

  const actions: ActionButton[] = [
    {
      label: "Edit",
      icon: <EditIcon />,
      onClick: (tag: Tag) => {
        setSelectedTag(tag);
        setFormOpen(true);
      },
      color: "primary",
    },
    {
      label: "Delete",
      icon: <DeleteIcon />,
      onClick: (tag: Tag) => {
        setSelectedTag(tag);
        setDeleteDialogOpen(true);
      },
      color: "error",
    },
  ];

  const handleAdd = () => {
    setSelectedTag(null);
    setFormOpen(true);
  };

  const handleFormSubmit = async (tagData: TagFormData) => {
    try {
      if (selectedTag) {
        await updateTag({ id: selectedTag.id, data: tagData }).unwrap();
      } else {
        await createTag(tagData).unwrap();
      }
      setFormOpen(false);
      setSelectedTag(null);
    } catch (error) {
      console.error("Error submitting tag:", error);
    }
  };

  const handleDelete = async () => {
    if (!selectedTag) return;

    try {
      await deleteTag(selectedTag.id).unwrap();
      setDeleteDialogOpen(false);
      setSelectedTag(null);
    } catch (error) {
      console.error("Error deleting tag:", error);
    }
  };

  const handlePageChange = (offset: number, limit: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("offset", offset.toString());
    params.set("limit", limit.toString());

    router.push(`${pathname}?${params.toString()}` as Route);
  };

  console.log(transformedTags);

  return (
    <div>
      <DataTable
        rows={transformedTags}
        columns={tagColumns}
        filterComponent={<TagsFilter />}
        actions={actions}
        onAdd={handleAdd}
        pagination={
          meta
            ? {
                offset: filters.offset,
                limit: filters.limit,
                total: meta.total,
                onPageChange: handlePageChange,
              }
            : undefined
        }
      />

      <TagForm
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        tag={selectedTag}
        isSubmitting={isCreating || isUpdating}
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Tag"
        message={`Are you sure you want to delete the tag "${selectedTag?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        severity="error"
        isLoading={isDeleting}
      />
    </div>
  );
}

export default TagsPage;