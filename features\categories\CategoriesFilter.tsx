"use client";

import { useCallback } from "react";
import { TextField, Box, Button } from "@mui/material";
import { useUrlState } from "@/hooks/useUrlState";

export default function CategoriesFilter() {
  const [formState, setFormState] = useUrlState({
    search: "",
  });

  const handleChange =
    (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormState((prev) => ({ ...prev, [field]: e.target.value, offset: "0" }));
    };

  const handleClear = useCallback(() => {
    setFormState({ search: "", offset: "0" });
  }, [setFormState]);

  return (
    <Box
      display="flex"
      flexDirection={{ xs: "column", md: "row" }}
      gap={2}
      alignItems="center"
      mb={2}
    >
      <TextField
        label="Search"
        variant="outlined"
        size="small"
        value={formState.search}
        onChange={handleChange("search")}
        sx={{ width: { xs: "100%", md: "auto" } }}
      />

      <Button variant="outlined" color="secondary" onClick={handleClear}>
        Clear
      </Button>
    </Box>
  );
}
