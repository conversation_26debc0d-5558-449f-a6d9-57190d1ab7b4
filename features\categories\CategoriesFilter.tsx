"use client";

import { useState, useEffect, useCallback } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { TextField, Box, Button } from "@mui/material";
import { Route } from "next";

function useDebounce<T>(value: T, delay = 500) {
  const [debounced, setDebounced] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => setDebounced(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);

  return debounced;
}

export default function CategoriesFilter() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();

  const [formState, setFormState] = useState({
    name: searchParams.get("name") || "",
    description: searchParams.get("description") || "",
  });

  const debouncedFormState = useDebounce(formState, 500);

  // update URL when debounced filters change
  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());

    // update only filter-related keys
    Object.entries(debouncedFormState).forEach(([key, value]) => {
      if (value) params.set(key, value);
      else params.delete(key);
    });

    // reset page on filter change
    params.delete("page");

    replace(`${pathname}?${params.toString()}` as Route);
  }, [debouncedFormState, pathname, replace, searchParams]);

  const handleChange =
    (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormState((prev) => ({ ...prev, [field]: e.target.value }));
    };

  const handleClear = useCallback(() => {
    setFormState({ name: "", description: "" });
    replace(pathname as Route);
  }, [replace, pathname]);

  return (
    <Box
      display="flex"
      flexDirection={{ xs: "column", md: "row" }}
      gap={2}
      alignItems="center"
      mb={2}
    >
      <TextField
        label="Name"
        variant="outlined"
        size="small"
        value={formState.name}
        onChange={handleChange("name")}
        sx={{ width: { xs: "100%", md: "auto" } }}
      />
      <TextField
        label="Description"
        variant="outlined"
        size="small"
        value={formState.description}
        onChange={handleChange("description")}
        sx={{ width: { xs: "100%", md: "auto" } }}
      />

      <Button variant="outlined" color="secondary" onClick={handleClear}>
        Clear
      </Button>
    </Box>
  );
}
