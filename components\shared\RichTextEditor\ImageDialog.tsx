import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
} from "@mui/material";

interface ImageDialogProps {
  open: boolean;
  imageUrl: string;
  onClose: () => void;
  onUrlChange: (url: string) => void;
  onAdd: () => void;
}

export default function ImageDialog({
  open,
  imageUrl,
  onClose,
  onUrlChange,
  onAdd,
}: ImageDialogProps) {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add Image</DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          margin="dense"
          label="Image URL"
          type="url"
          fullWidth
          variant="outlined"
          value={imageUrl}
          onChange={(e) => onUrlChange(e.target.value)}
          placeholder="https://example.com/image.jpg"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={onAdd} variant="contained">
          Add Image
        </Button>
      </DialogActions>
    </Dialog>
  );
}
