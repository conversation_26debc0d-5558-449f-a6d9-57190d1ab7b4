import MainLayout from "@/components/layout/MainLayout";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import React from "react";

async function layout({ children }: { children: React.ReactNode }) {
  const cookieStore = await cookies();

  const refreshToken = cookieStore.get("refreshToken")?.value;

  if (!refreshToken) {
    return redirect("/login");
  }

  return <MainLayout>{children}</MainLayout>;
}

export default layout;
