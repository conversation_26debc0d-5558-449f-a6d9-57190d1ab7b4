import type { BaseQueryFn, FetchArgs, FetchBaseQueryError } from '@reduxjs/toolkit/query'
import { rawBaseQuery } from './baseQuery'
import { logout, setToken } from '../slices/authSlice'

// Wrap it to handle custom logic (e.g., token refresh)
export const customFetchBaseQuery: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  let result = await rawBaseQuery(args, api, extraOptions)

  if (result.error && result.error.status === 401) {
    const refreshResult = await rawBaseQuery('/auth/refresh/', api, extraOptions)
    if (refreshResult.data) {
      // store new token
      api.dispatch(setToken((refreshResult.data as { accessToken: string, refreshToken: string }).accessToken))

      result = await rawBaseQuery(args, api, extraOptions)
    } else {
      api.dispatch(logout())
    }
  }

  return result
}
