"use client";

import { useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { TextField, Box } from "@mui/material";
import FormDialog from "@/components/shared/FormDialog";
import { Category } from "@/types";
import { categorySchema, CategoryFormData } from "@/lib/schemas";

interface CategoryFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (category: CategoryFormData) => void;
  category?: Category | null;
  isSubmitting?: boolean;
}

export default function CategoryForm({
  open,
  onClose,
  onSubmit,
  category,
  isSubmitting = false,
}: CategoryFormProps) {
  // React Hook Form setup
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CategoryFormData>({
    resolver: yupResolver(categorySchema),
    defaultValues: {
      name: "",
    },
  });

  // Reset form when category changes or dialog opens
  useEffect(() => {
    if (category) {
      reset({
        name: category.name,
      });
    } else {
      reset({
        name: "",
      });
    }
  }, [category, open, reset]);

  // Form submit handler
  const onFormSubmit = (data: CategoryFormData) => {
    onSubmit(data);
  };

  const handleCancel = () => {
    reset({ name: "" });
    onClose();
  };

  return (
    <FormDialog
      open={open}
      onClose={onClose}
      title={category ? "Edit Category" : "Add Category"}
      onSubmit={handleSubmit(onFormSubmit)}
      onCancel={handleCancel}
      isSubmitting={isSubmitting}
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              label="Name"
              error={!!errors.name}
              helperText={errors.name?.message || "Category name (slug will be generated automatically)"}
              fullWidth
              required
              autoFocus
            />
          )}
        />
      </Box>
    </FormDialog>
  );
}
