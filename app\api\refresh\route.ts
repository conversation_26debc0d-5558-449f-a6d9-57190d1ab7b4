import { cookies } from "next/headers";

export async function POST() {
  const cookieStore = await cookies();
  const refreshToken = cookieStore.get("refreshToken")?.value;

  if (!refreshToken) {
    return new Response(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
    });
  }

  try {
    const response = await fetch("http://localhost:3000/auth/refresh/", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ refreshToken }),
    });

    if (!response.ok) {
      throw new Error("Failed to refresh token");
    }

    const data = await response.json();
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: {
        "Set-Cookie": `refreshToken=${data.refreshToken}; HttpOnly; Path=/; Max-Age=3600`,
      },
    });
  } catch(errr) {
    console.log("CATCHED ", errr);
    return new Response(JSON.stringify({ error: "Failed to refresh token" }), {
      status: 500,
    });
  }
}
