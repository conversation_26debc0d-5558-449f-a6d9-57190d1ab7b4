import * as yup from "yup";

// Article validation schema
export const articleSchema = yup.object().shape({
  title: yup
    .string()
    .required("Title is required")
    .min(1, "Title cannot be empty"),
  content: yup
    .string()
    .required("Content is required")
    .min(1, "Content cannot be empty"),
  imageUrl: yup
    .string()
    .url("Must be a valid URL")
    .optional()
    .default("")
    .transform((value) => value === "" ? "" : value),
  categoryId: yup
    .number()
    .required("Category is required")
    .min(1, "Please select a category"),
  tagIds: yup.array().of(yup.number().required()).default([]),
});

// Category validation schema
export const categorySchema = yup.object({
  name: yup
    .string()
    .required("Name is required")
    .min(1, "Name cannot be empty"),
});

// Tag validation schema
export const tagSchema = yup.object({
  name: yup
    .string()
    .required("Name is required")
    .min(1, "Name cannot be empty"),
});

// Type definitions for form data
export type ArticleFormData = yup.InferType<typeof articleSchema>;
export type CategoryFormData = yup.InferType<typeof categorySchema>;
export type TagFormData = yup.InferType<typeof tagSchema>;
