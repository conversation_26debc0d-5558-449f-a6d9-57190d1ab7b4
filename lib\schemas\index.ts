import * as yup from "yup";

// First define the interface exactly how you want it
export interface ArticleFormData {
  title: string;
  content: string;
  image?: File | undefined; // Optional, no null needed
  category: number;
  tags: number[];
}

export interface CategoryFormData {
  name: string;
}

export interface TagFormData {
  name: string;
}

// Then create the schema without strict typing - let yup handle validation
export const articleSchema = yup.object().shape({
  title: yup
    .string()
    .required("Title is required")
    .min(1, "Title cannot be empty"),
  content: yup
    .string()
    .required("Content is required")
    .min(1, "Content cannot be empty"),
  image: yup
    .mixed()
    .test("image-validation", "Please select a valid image", function (value) {
      const { isEdit, hasExistingImage } = this.options.context || {};

      // Check if a file is present (File object or valid object)
      const isFilePresent = value instanceof File || (!!value && typeof value === "object");

      // For create mode (new article)
      if (!isEdit) {
        // Image is optional for new articles, so if no value provided, it's valid
        if (!value) return true;
        // If value is provided, it should be a valid file
        return isFilePresent;
      }

      // For edit mode (existing article)
      if (isEdit) {
        // If article has existing image and no new file is uploaded, that's fine
        if (hasExistingImage && !value) return true;
        // If new file is provided, validate it
        if (value) return isFilePresent;
        // If no existing image and no new file, that's also fine (optional)
        return true;
      }

      return true;
    })
    .test("fileSize", "File size must be less than 5MB", function(value) {
      if (!value || !(value instanceof File)) return true;
      return value.size <= 5 * 1024 * 1024; // 5MB limit
    })
    .test("fileType", "Only image files are allowed", function(value) {
      if (!value || !(value instanceof File)) return true;
      return value.type.startsWith("image/");
    }),
  category: yup
    .number()
    .required("Category is required")
    .min(1, "Please select a category"),
  tags: yup
    .array()
    .of(yup.number().required())
    .required()
    .default([]),
});

export const categorySchema = yup.object().shape({
  name: yup
    .string()
    .required("Name is required")
    .min(1, "Name cannot be empty"),
});

export const tagSchema = yup.object().shape({
  name: yup
    .string()
    .required("Name is required")
    .min(1, "Name cannot be empty"),
});