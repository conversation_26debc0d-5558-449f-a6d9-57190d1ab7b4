import * as yup from "yup";

// Article validation schema
export const articleSchema = yup.object().shape({
  title: yup
    .string()
    .required("Title is required")
    .min(1, "Title cannot be empty"),
  content: yup
    .string()
    .required("Content is required")
    .min(1, "Content cannot be empty"),
  image: yup
    .mixed<File>()
    .optional()
    .nullable()
    .test("fileSize", "File size must be less than 5MB", (value) => {
      if (!value) return true; // Optional field
      return value.size <= 5 * 1024 * 1024; // 5MB limit
    })
    .test("fileType", "Only image files are allowed", (value) => {
      if (!value) return true; // Optional field
      return value.type.startsWith("image/");
    }),
  category: yup
    .number()
    .required("Category is required")
    .min(1, "Please select a category"),
  tags: yup.array().of(yup.number().required()).default([]),
});

// Category validation schema
export const categorySchema = yup.object({
  name: yup
    .string()
    .required("Name is required")
    .min(1, "Name cannot be empty"),
});

// Tag validation schema
export const tagSchema = yup.object({
  name: yup
    .string()
    .required("Name is required")
    .min(1, "Name cannot be empty"),
});

// Type definitions for form data
export type ArticleFormData = yup.InferType<typeof articleSchema>;
export type CategoryFormData = yup.InferType<typeof categorySchema>;
export type TagFormData = yup.InferType<typeof tagSchema>;
