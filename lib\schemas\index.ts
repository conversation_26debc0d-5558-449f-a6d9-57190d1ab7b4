import * as yup from "yup";

// First define the interface exactly how you want it
export interface ArticleFormData {
  title: string;
  content: string;
  image?: File; // Optional, no null needed
  category: number;
  tags: number[];
}

export interface CategoryFormData {
  name: string;
}

export interface TagFormData {
  name: string;
}

// Then create the schema without strict typing - let yup handle validation
export const articleSchema = yup.object().shape({
  title: yup
    .string()
    .required("Title is required")
    .min(1, "Title cannot be empty"),
  content: yup
    .string()
    .required("Content is required")
    .min(1, "Content cannot be empty"),
  image: yup
    .mixed()
    .notRequired()
    .test("fileSize", "File size must be less than 5MB", function(value) {
      if (!value) return true;
      return value.size <= 5 * 1024 * 1024;
    })
    .test("fileType", "Only image files are allowed", function(value) {
      if (!value) return true;
      return value.type.startsWith("image/");
    }),
  category: yup
    .number()
    .required("Category is required")
    .min(1, "Please select a category"),
  tags: yup
    .array()
    .of(yup.number().required())
    .required()
    .default([]),
});

export const categorySchema = yup.object().shape({
  name: yup
    .string()
    .required("Name is required")
    .min(1, "Name cannot be empty"),
});

export const tagSchema = yup.object().shape({
  name: yup
    .string()
    .required("Name is required")
    .min(1, "Name cannot be empty"),
});