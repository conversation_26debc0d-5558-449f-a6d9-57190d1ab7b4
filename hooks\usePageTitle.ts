"use client";

import { usePathname } from "next/navigation";

export function usePageTitle() {
  const pathname = usePathname();

  const getPageTitle = () => {
    switch (pathname) {
      case "/dashboard":
        return "Dashboard";
      case "/dashboard/articles":
        return "Articles";
      case "/dashboard/categories":
        return "Categories";
      case "/dashboard/tags":
        return "Tags";
      case "/dashboard/subscriptions":
        return "Subscriptions";
      default:
        return "Dashboard";
    }
  };

  return getPageTitle();
}
