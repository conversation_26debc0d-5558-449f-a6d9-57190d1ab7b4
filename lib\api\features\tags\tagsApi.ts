import { protectedApiSlice } from '../../protected/protectedApiSlice';
import { Tag, TagFormData, TagResponse, TagFilters } from '@/types';

export const tagsApi = protectedApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getTags: builder.query<TagResponse, TagFilters>({
      query: (params) => ({
        url: "/tags/",
        method: "GET",
        params,
      }),
      providesTags: ['Tag'],
    }),
    getTag: builder.query<Tag, number>({
      query: (id) => `/tags/${id}/`,
      providesTags: (result, error, id) => [{ type: 'Tag', id }],
    }),
    createTag: builder.mutation<Tag, TagFormData>({
      query: (tag) => ({
        url: '/tags/create/',
        method: 'POST',
        body: tag,
      }),
      invalidatesTags: ['Tag'],
    }),
    updateTag: builder.mutation<Tag, { id: number; data: TagFormData }>({
      query: ({ id, data }) => ({
        url: `/tags/${id}/`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Tag', id }],
    }),
    deleteTag: builder.mutation<void, number>({
      query: (id) => ({
        url: `/tags/${id}/`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Tag'],
    }),
  }),
});

export const {
  useGetTagsQuery,
  useGetTagQuery,
  useCreateTagMutation,
  useUpdateTagMutation,
  useDeleteTagMutation,
} = tagsApi;
