import { protectedApiSlice } from '../../protected/protectedApiSlice';
import { SubscriptionResponse, SubscriptionFilters } from '@/types';

export const subscriptionsApi = protectedApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getSubscriptions: builder.query<SubscriptionResponse, SubscriptionFilters>({
      query: (filters) => {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
          if (value) {
            params.append(key, value.toString());
          }
        });
        return `/subscriptions/?${params.toString()}`;
      },
      providesTags: ['Subscription'],
    }),
    deleteSubscription: builder.mutation<void, number>({
      query: (id) => ({
        url: `/subscriptions/${id}/`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Subscription'],
    }),
  }),
});

export const {
  useGetSubscriptionsQuery,
  useDeleteSubscriptionMutation,
} = subscriptionsApi;
