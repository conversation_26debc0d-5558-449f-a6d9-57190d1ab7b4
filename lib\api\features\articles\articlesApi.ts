import { protectedApiSlice } from "../../protected/protectedApiSlice";
import {
  Article,
  ArticleFormData,
  ArticleResponse,
  ArticleFilters,
} from "@/types";

export const articlesApi = protectedApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getArticles: builder.query<ArticleResponse, ArticleFilters>({
      query: (params) => ({
        url: "/articles/",
        method: "GET",
        params: params,
      }),
      providesTags: ["Article"],
    }),
    getArticle: builder.query<Article, number>({
      query: (id) => `/articles/${id}/`,
      providesTags: (result, error, id) => [{ type: "Article", id }],
    }),
    createArticle: builder.mutation<Article, ArticleFormData>({
      query: (article) => ({
        url: "/articles/create/",
        method: "POST",
        body: article,
      }),
      invalidatesTags: ["Article"],
    }),
    updateArticle: builder.mutation<
      Article,
      { id: number; data: ArticleFormData }
    >({
      query: ({ id, data }) => ({
        url: `/articles/${id}/`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Article", id }],
    }),
    deleteArticle: builder.mutation<void, number>({
      query: (id) => ({
        url: `/articles/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["Article"],
    }),
  }),
});

export const {
  useGetArticlesQuery,
  useGetArticleQuery,
  useCreateArticleMutation,
  useUpdateArticleMutation,
  useDeleteArticleMutation,
} = articlesApi;
