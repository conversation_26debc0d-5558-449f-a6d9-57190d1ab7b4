import { protectedApiSlice } from "../../protected/protectedApiSlice";
import {
  Article,
  ArticleFormData,
  ArticleResponse,
  ArticleFilters,
} from "@/types";

export const articlesApi = protectedApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getArticles: builder.query<ArticleResponse, ArticleFilters>({
      query: (params) => ({
        url: "/articles/",
        method: "GET",
        params: params,
      }),
      providesTags: ["Article"],
    }),
    getArticle: builder.query<Article, number>({
      query: (id) => `/articles/${id}/`,
      providesTags: (result, error, id) => [{ type: "Article", id }],
    }),
    createArticle: builder.mutation<Article, { data: ArticleFormData; imageFile?: File }>({
      query: ({ data, imageFile }) => {
        const formData = new FormData();
        formData.append("title", data.title);
        formData.append("content", data.content);
        formData.append("category", data.category.toString());
        data.tags.forEach((tagId) => {
          formData.append("tags", tagId.toString());
        });
        if (imageFile) {
          formData.append("image", imageFile);
        }

        return {
          url: "/articles/create/",
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: ["Article"],
    }),
    updateArticle: builder.mutation<
      Article,
      { id: number; data: ArticleFormData; imageFile?: File }
    >({
      query: ({ id, data, imageFile }) => {
        const formData = new FormData();
        formData.append("title", data.title);
        formData.append("content", data.content);
        formData.append("category", data.category.toString());
        data.tags.forEach((tagId) => {
          formData.append("tags", tagId.toString());
        });
        if (imageFile) {
          formData.append("image", imageFile);
        }

        return {
          url: `/articles/${id}/`,
          method: "PATCH",
          body: formData,
        };
      },
      invalidatesTags: (result, error, { id }) => [{ type: "Article", id }],
    }),
    deleteArticle: builder.mutation<void, number>({
      query: (id) => ({
        url: `/articles/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["Article"],
    }),
  }),
});

export const {
  useGetArticlesQuery,
  useGetArticleQuery,
  useCreateArticleMutation,
  useUpdateArticleMutation,
  useDeleteArticleMutation,
} = articlesApi;
