"use client";

import { useState, useMemo } from "react";
import { useSearchParams, usePathname, useRouter } from "next/navigation";
import { Box, CircularProgress, Alert } from "@mui/material";
import DataTable, { Column, ActionButton } from "@/components/shared/DataTable";
import SubscriptionsFilter from "@/features/subscriptions/SubscriptionsFilter";
import ConfirmDialog from "@/components/shared/ConfirmDialog";
import DeleteIcon from "@mui/icons-material/Delete";
import { Subscription, SubscriptionFilters } from "@/types";
import {
  useGetSubscriptionsQuery,
  useDeleteSubscriptionMutation,
} from "@/lib/api/features/subscriptions/subscriptionsApi";
import { Route } from "next";

function SubscriptionsPage() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const filters = useMemo((): SubscriptionFilters => {
    return {
      email: searchParams.get("email") || undefined,
      offset: parseInt(searchParams.get("offset") || "0"),
      limit: parseInt(searchParams.get("limit") || "10"),
    };
  }, [searchParams]);

  const { data: subscriptionsResponse, isLoading, error } = useGetSubscriptionsQuery(filters);
  const [deleteSubscription, { isLoading: isDeleting }] = useDeleteSubscriptionMutation();

  const subscriptions = subscriptionsResponse?.data || [];
  const meta = subscriptionsResponse?.meta;

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<Subscription | null>(null);

  if (isLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: 400,
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load subscriptions. Please try again.
      </Alert>
    );
  }

  const subscriptionColumns: Column[] = [
    { accessor: "email", label: "Email", minWidth: 250 },
    { accessor: "createdAt", label: "Created At", minWidth: 150 },
    { accessor: "updatedAt", label: "Updated At", minWidth: 150 },
  ];

  const actions: ActionButton[] = [
    {
      label: "Delete",
      icon: <DeleteIcon />,
      onClick: (subscription: Subscription) => {
        setSelectedSubscription(subscription);
        setDeleteDialogOpen(true);
      },
      color: "error",
    },
  ];

  const handleDelete = async () => {
    if (!selectedSubscription) return;

    try {
      await deleteSubscription(selectedSubscription.id).unwrap();
      setDeleteDialogOpen(false);
      setSelectedSubscription(null);
    } catch (error) {
      console.error("Error deleting subscription:", error);
    }
  };

  const handlePageChange = (offset: number, limit: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("offset", offset.toString());
    params.set("limit", limit.toString());

    router.push(`${pathname}?${params.toString()}` as Route);
  };

  return (
    <div>
      <DataTable
        rows={subscriptions}
        columns={subscriptionColumns}
        filterComponent={<SubscriptionsFilter />}
        actions={actions}
        pagination={
          meta
            ? {
                offset: filters.offset,
                limit: filters.limit,
                total: meta.total,
                onPageChange: handlePageChange,
              }
            : undefined
        }
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Subscription"
        message={`Are you sure you want to delete the subscription "${selectedSubscription?.email}"? This action cannot be undone.`}
        confirmText="Delete"
        severity="error"
        isLoading={isDeleting}
      />
    </div>
  );
}

export default SubscriptionsPage;