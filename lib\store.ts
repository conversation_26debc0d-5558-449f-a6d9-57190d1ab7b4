import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import {
  authApi,
  protectedApiSlice,
  publicApiSlice,
} from "./api";
import authSlice from "./api/slices/authSlice";

export const store = configureStore({
  reducer: {
    [protectedApiSlice.reducerPath]: protectedApiSlice.reducer,
    [publicApiSlice.reducerPath]: publicApiSlice.reducer,
    [authApi.reducerPath]: authApi.reducer,
    auth: authSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      protectedApiSlice.middleware,
      publicApiSlice.middleware,
      authApi.middleware
    ),
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
