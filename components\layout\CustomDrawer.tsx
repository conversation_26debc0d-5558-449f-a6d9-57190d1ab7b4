import {
  Divider,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
} from "@mui/material";
import ArticleIcon from "@mui/icons-material/Article";
import CategoryIcon from "@mui/icons-material/Category";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import SubscriptionsIcon from "@mui/icons-material/Subscriptions";
import React from "react";
import Image from "next/image";
import Link from "next/link";

const navigationItems = [
  { text: "Articles", href: "/dashboard/articles" as const, icon: <ArticleIcon /> },
  { text: "Categories", href: "/dashboard/categories" as const, icon: <CategoryIcon /> },
  { text: "Tags", href: "/dashboard/tags" as const, icon: <LocalOfferIcon /> },
  { text: "Subscriptions", href: "/dashboard/subscriptions" as const, icon: <SubscriptionsIcon /> },
];

function CustomDrawer() {
  return (
    <div>
      <Toolbar component={Link} href={"/dashboard"}>
        <Image
          src="/kodaze.png"
          alt="Logo"
          width={500}
          height={300}
          priority
          className="h-[30] w-auto"
        />
      </Toolbar>
      <Divider />
      <List>
        {navigationItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <Link href={item.href} style={{ textDecoration: 'none', color: 'inherit', width: '100%' }}>
              <ListItemButton>
                <ListItemIcon>
                  {item.icon}
                </ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItemButton>
            </Link>
          </ListItem>
        ))}
      </List>
    </div>
  );
}

export default CustomDrawer;
