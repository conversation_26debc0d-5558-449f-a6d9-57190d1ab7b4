import { Editor } from "@tiptap/react";
import { Icon<PERSON><PERSON>on, Too<PERSON><PERSON>, Divider } from "@mui/material";
import {
  FormatBold,
  FormatItalic,
  FormatListBulleted,
  FormatListNumbered,
  Link as LinkIcon,
  Image as ImageIcon,
  FormatAlignLeft,
  FormatAlignCenter,
  FormatAlignRight,
  Code,
  FormatQuote,
  Undo,
  Redo,
} from "@mui/icons-material";

interface EditorToolbarProps {
  editor: Editor;
  selectionUpdate: number;
  onLinkClick: () => void;
  onImageClick: () => void;
}

export default function EditorToolbar({
  editor,
  selectionUpdate,
  onLinkClick,
  onImageClick,
}: EditorToolbarProps) {
  return (
    <Toolbar
      key={selectionUpdate} // only toolbar re-renders
      variant="dense"
      sx={{
        minHeight: 48,
        backgroundColor: "#f5f5f5",
        borderBottom: "1px solid #e0e0e0",
        gap: 0.5,
        flexWrap: "wrap",
      }}
    >
      {/* Headings */}
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
        color={editor.isActive("heading", { level: 1 }) ? "primary" : "default"}
      >
        H1
      </IconButton>
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
        color={editor.isActive("heading", { level: 2 }) ? "primary" : "default"}
      >
        H2
      </IconButton>
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
        color={editor.isActive("heading", { level: 3 }) ? "primary" : "default"}
      >
        H3
      </IconButton>

      {/* Text styles */}
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().toggleBold().run()}
        color={editor.isActive("bold") ? "primary" : "default"}
      >
        <FormatBold />
      </IconButton>
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().toggleItalic().run()}
        color={editor.isActive("italic") ? "primary" : "default"}
      >
        <FormatItalic />
      </IconButton>
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().toggleCode().run()}
        color={editor.isActive("code") ? "primary" : "default"}
      >
        <Code />
      </IconButton>

      <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

      {/* Lists */}
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().toggleBulletList().run()}
        color={editor.isActive("bulletList") ? "primary" : "default"}
      >
        <FormatListBulleted />
      </IconButton>
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().toggleOrderedList().run()}
        color={editor.isActive("orderedList") ? "primary" : "default"}
      >
        <FormatListNumbered />
      </IconButton>

      <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

      {/* Alignment */}
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().setTextAlign("left").run()}
        color={editor.isActive({ textAlign: "left" }) ? "primary" : "default"}
      >
        <FormatAlignLeft />
      </IconButton>
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().setTextAlign("center").run()}
        color={editor.isActive({ textAlign: "center" }) ? "primary" : "default"}
      >
        <FormatAlignCenter />
      </IconButton>
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().setTextAlign("right").run()}
        color={editor.isActive({ textAlign: "right" }) ? "primary" : "default"}
      >
        <FormatAlignRight />
      </IconButton>

      <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

      {/* Quote */}
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().toggleBlockquote().run()}
        color={editor.isActive("blockquote") ? "primary" : "default"}
      >
        <FormatQuote />
      </IconButton>

      {/* Link + Image */}
      <IconButton size="small" onClick={onLinkClick}>
        <LinkIcon />
      </IconButton>
      <IconButton size="small" onClick={onImageClick}>
        <ImageIcon />
      </IconButton>

      <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />

      {/* Undo/Redo */}
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().undo().run()}
        disabled={!editor.can().undo()}
      >
        <Undo />
      </IconButton>
      <IconButton
        size="small"
        onClick={() => editor.chain().focus().redo().run()}
        disabled={!editor.can().redo()}
      >
        <Redo />
      </IconButton>
    </Toolbar>
  );
}
