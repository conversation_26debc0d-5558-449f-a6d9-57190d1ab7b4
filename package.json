{"name": "kodaze-admin-front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build --turbopack", "start": "next start -p 3001", "lint": "eslint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.2.1", "@mui/icons-material": "^7.3.2", "@mui/material": "^7.3.2", "@mui/x-data-grid": "^8.11.1", "@reduxjs/toolkit": "^2.9.0", "@tiptap/extension-bullet-list": "^3.4.1", "@tiptap/extension-image": "^3.4.1", "@tiptap/extension-link": "^3.4.1", "@tiptap/extension-list-item": "^3.4.1", "@tiptap/extension-ordered-list": "^3.4.1", "@tiptap/extension-text-align": "^3.4.1", "@tiptap/pm": "^3.4.1", "@tiptap/react": "^3.4.1", "@tiptap/starter-kit": "^3.4.1", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-redux": "^9.2.0", "yup": "^1.7.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}