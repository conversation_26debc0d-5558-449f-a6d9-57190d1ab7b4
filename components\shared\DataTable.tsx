import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import { IconButton, Box, Fab, Pagination } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";

export type Column = {
  accessor: string;
  label: string;
  minWidth?: number;
  align?: "left" | "right" | "center";
};

export type ActionButton = {
  label: string;
  icon: React.ReactNode;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onClick: (row: any) => void;
  color?: "primary" | "secondary" | "error" | "warning" | "info" | "success";
};

export default function DataTable({
  rows = [],
  columns = [],
  filterComponent,
  actions = [],
  onAdd,
  pagination,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  rows?: any[];
  columns?: Column[];
  filterComponent?: React.ReactNode;
  actions?: ActionButton[];
  onAdd?: () => void;
  title?: string;
  pagination?: {
    limit: number;
    offset: number;
    total: number;
    onPageChange: (offset: number, limit: number) => void;
  };
}) {
  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Box sx={{ flex: 1 }}>{filterComponent}</Box>
        {onAdd && (
          <Fab color="primary" aria-label="add" onClick={onAdd} sx={{ ml: 2 }}>
            <AddIcon />
          </Fab>
        )}
      </Box>

      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.accessor}
                  align={column.align}
                  style={{ minWidth: column.minWidth }}
                >
                  {column.label}
                </TableCell>
              ))}
              {actions.length > 0 && (
                <TableCell align="center" style={{ minWidth: 120 }}>
                  Actions
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.length > 0 &&
              rows.map((row, rowIndex) => (
                <TableRow
                  key={rowIndex}
                  sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                >
                  {columns.map((col, colIndex) => (
                    <TableCell
                      key={colIndex}
                      component={colIndex === 0 ? "th" : "td"}
                      scope={colIndex === 0 ? "row" : undefined}
                      className="!truncate"
                    >
                      {row[col.accessor]}
                    </TableCell>
                  ))}
                  {actions.length > 0 && (
                    <TableCell align="center">
                      <Box
                        sx={{
                          display: "flex",
                          gap: 1,
                          justifyContent: "center",
                        }}
                      >
                        {actions.map((action, actionIndex) => (
                          <IconButton
                            key={actionIndex}
                            size="small"
                            color={action.color || "primary"}
                            onClick={() => action.onClick(row)}
                            title={action.label}
                          >
                            {action.icon}
                          </IconButton>
                        ))}
                      </Box>
                    </TableCell>
                  )}
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>

      {pagination && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 3 }}>
          <Pagination
            count={Math.ceil(pagination.total / pagination.limit)}
            page={Math.floor(pagination.offset / pagination.limit) + 1}
            onChange={(e, page) => {
              const newOffset = (page - 1) * pagination.limit;
              pagination.onPageChange(newOffset, pagination.limit);
            }}
            color="primary"
            size="large"
            showFirstButton
            showLastButton
          />
        </Box>
      )}
    </>
  );
}
