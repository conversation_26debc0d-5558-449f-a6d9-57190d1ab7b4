"use client";

import { useRouter } from "next/navigation";
import {
  Box,
  Paper,
  Typography,
  Button,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArticleForm from "@/features/articles/ArticleForm";
import { ArticleFormData } from "@/types";
import { useCreateArticleMutation } from "@/lib/api/features/articles/articlesApi";

export default function CreateArticlePage() {
  const router = useRouter();
  const [createArticle, { isLoading: isSubmitting }] = useCreateArticleMutation();

  const handleSubmit = async (articleData: ArticleFormData, imageFile?: File | null) => {
    try {
      await createArticle({ data: articleData, imageFile: imageFile || undefined }).unwrap();
      router.push("/dashboard/articles");
    } catch (error) {
      console.error("Error creating article:", error);
    }
  };

  const handleCancel = () => {
    router.push("/dashboard/articles");
  };

  return (
    <Box sx={{ maxWidth: 1000, mx: "auto" }}>
      <Box sx={{ mb: 3, display: "flex", alignItems: "center", gap: 2 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleCancel}
          variant="outlined"
        >
          Back to Articles
        </Button>
        <Typography variant="h4" component="h1">
          Create Article
        </Typography>
      </Box>

      <Paper sx={{ p: 4 }}>
        <ArticleForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
          submitText="Create Article"
          cancelText="Cancel"
        />
      </Paper>
    </Box>
  );
}
