import AppBar from "@mui/material/AppBar";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import Toolbar from "@mui/material/Toolbar";
import Typography from "@mui/material/Typography";
import { MaterialUISwitch } from "./MaterialUiSwitch";
import AccountMenu from "./AccountMenu";
import { usePageTitle } from "@/hooks/usePageTitle";

function LayoutHeader({
  handleDrawerToggle,
  drawerWidth,
}: {
  handleDrawerToggle: () => void;
  drawerWidth: number;
}) {
  const pageTitle = usePageTitle();
  return (
    <AppBar
      className="!bg-white !text-black !shadow-none !border-b-[1px] !border-gray-200"
      position="fixed"
      sx={{
        width: { sm: `calc(100% - ${drawerWidth}px)` },
        ml: { sm: `${drawerWidth}px` },
      }}
    >
      <Toolbar className="justify-between">
        <div className="flex items-center overflow-hidden">
          <IconButton
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: "none" } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            {pageTitle}
          </Typography>
        </div>
        <div className="flex items-center">
          <MaterialUISwitch
            sx={{ m: 1 }}
            // defaultChecked
            //   onChange={handleThemeChange}
          />
          <AccountMenu />
        </div>
      </Toolbar>
    </AppBar>
  );
}

export default LayoutHeader;
