"use client";

import { useState, useMemo } from "react";
import { useSearchParams, usePathname, useRouter } from "next/navigation";
import { Box, CircularProgress, Alert } from "@mui/material";
import DataTable, { Column, ActionButton } from "@/components/shared/DataTable";
import CategoriesFilter from "@/features/categories/CategoriesFilter";
import CategoryForm from "@/features/categories/CategoryForm";
import ConfirmDialog from "@/components/shared/ConfirmDialog";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { Category, CategoryFormData, CategoryFilters } from "@/types";
import {
  useGetCategoriesQuery,
  useCreateCategoryMutation,
  useUpdateCategoryMutation,
  useDeleteCategoryMutation
} from "@/lib/api/features/categories/categoriesApi";
import { Route } from "next";

function CategoriesPage() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const filters = useMemo((): CategoryFilters => {
    return {
      search: searchParams.get("search") || undefined,
      offset: parseInt(searchParams.get("offset") || "0"),
      limit: parseInt(searchParams.get("limit") || "10"),
    };
  }, [searchParams]);

  // RTK Query hooks
  const { data: categoriesResponse, isLoading, error } = useGetCategoriesQuery(filters);
  const [createCategory, { isLoading: isCreating }] = useCreateCategoryMutation();
  const [updateCategory, { isLoading: isUpdating }] = useUpdateCategoryMutation();
  const [deleteCategory, { isLoading: isDeleting }] = useDeleteCategoryMutation();

  // Extract data from response
  const categories = categoriesResponse?.data || [];
  const meta = categoriesResponse?.meta;

  const [formOpen, setFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  // Show loading state
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load categories. Please try again.
      </Alert>
    );
  }

  const categoryColumns: Column[] = [
    { accessor: "name", label: "Name", minWidth: 150 },
    { accessor: "slug", label: "Slug", minWidth: 150 },
    { accessor: "createdAt", label: "Created At", minWidth: 150 },
    { accessor: "updatedAt", label: "Updated At", minWidth: 150 },
  ];

  const actions: ActionButton[] = [
    {
      label: "Edit",
      icon: <EditIcon />,
      onClick: (category: Category) => {
        setSelectedCategory(category);
        setFormOpen(true);
      },
      color: "primary",
    },
    {
      label: "Delete",
      icon: <DeleteIcon />,
      onClick: (category: Category) => {
        setSelectedCategory(category);
        setDeleteDialogOpen(true);
      },
      color: "error",
    },
  ];

  const handleAdd = () => {
    setSelectedCategory(null);
    setFormOpen(true);
  };

  const handleFormSubmit = async (categoryData: CategoryFormData) => {
    try {
      if (selectedCategory) {
        await updateCategory({ id: selectedCategory.id, data: categoryData }).unwrap();
      } else {
        await createCategory(categoryData).unwrap();
      }
      setFormOpen(false);
      setSelectedCategory(null);
    } catch (error) {
      console.error("Error submitting category:", error);
    }
  };

  const handleDelete = async () => {
    if (!selectedCategory) return;

    try {
      await deleteCategory(selectedCategory.id).unwrap();
      setDeleteDialogOpen(false);
      setSelectedCategory(null);
    } catch (error) {
      console.error("Error deleting category:", error);
    }
  };

  const handlePageChange = (offset: number, limit: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("offset", offset.toString());
    params.set("limit", limit.toString());

    router.push(`${pathname}?${params.toString()}` as Route);
  };

  return (
    <div>
      <DataTable
        rows={categories}
        columns={categoryColumns}
        filterComponent={<CategoriesFilter />}
        actions={actions}
        onAdd={handleAdd}
        pagination={
          meta
            ? {
                offset: filters.offset,
                limit: filters.limit,
                total: meta.total,
                onPageChange: handlePageChange,
              }
            : undefined
        }
      />

      <CategoryForm
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        category={selectedCategory}
        isSubmitting={isCreating || isUpdating}
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Category"
        message={`Are you sure you want to delete the category "${selectedCategory?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        severity="error"
        isLoading={isDeleting}
      />
    </div>
  );
}

export default CategoriesPage;