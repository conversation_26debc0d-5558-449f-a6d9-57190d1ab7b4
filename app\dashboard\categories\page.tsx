"use client";

import { useState } from "react";
import { Box, CircularProgress, Alert } from "@mui/material";
import DataTable, { Column, ActionButton } from "@/components/shared/DataTable";
import CategoriesFilter from "@/features/categories/CategoriesFilter";
import CategoryForm from "@/features/categories/CategoryForm";
import ConfirmDialog from "@/components/shared/ConfirmDialog";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { Category, CategoryFormData } from "@/types";
import {
  useGetCategoriesQuery,
  useCreateCategoryMutation,
  useUpdateCategoryMutation,
  useDeleteCategoryMutation
} from "@/lib/api/features/categories/categoriesApi";

function CategoriesPage() {
  // RTK Query hooks
  const { data: categoriesResponse, isLoading, error } = useGetCategoriesQuery();
  const [createCategory, { isLoading: isCreating }] = useCreateCategoryMutation();
  const [updateCategory, { isLoading: isUpdating }] = useUpdateCategoryMutation();
  const [deleteCategory, { isLoading: isDeleting }] = useDeleteCategoryMutation();

  // Extract data from response
  const categories = categoriesResponse?.data || [];

  const [formOpen, setFormOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  // Show loading state
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load categories. Please try again.
      </Alert>
    );
  }

  const categoryColumns: Column[] = [
    { accessor: "name", label: "Name", minWidth: 150 },
    { accessor: "slug", label: "Slug", minWidth: 150 },
    { accessor: "createdAt", label: "Created At", minWidth: 150 },
    { accessor: "updatedAt", label: "Updated At", minWidth: 150 },
  ];

  const actions: ActionButton[] = [
    {
      label: "Edit",
      icon: <EditIcon />,
      onClick: (category: Category) => {
        setSelectedCategory(category);
        setFormOpen(true);
      },
      color: "primary",
    },
    {
      label: "Delete",
      icon: <DeleteIcon />,
      onClick: (category: Category) => {
        setSelectedCategory(category);
        setDeleteDialogOpen(true);
      },
      color: "error",
    },
  ];

  const handleAdd = () => {
    setSelectedCategory(null);
    setFormOpen(true);
  };

  const handleFormSubmit = async (categoryData: CategoryFormData) => {
    try {
      if (selectedCategory) {
        await updateCategory({ id: selectedCategory.id, data: categoryData }).unwrap();
      } else {
        await createCategory(categoryData).unwrap();
      }
      setFormOpen(false);
      setSelectedCategory(null);
    } catch (error) {
      console.error("Error submitting category:", error);
    }
  };

  const handleDelete = async () => {
    if (!selectedCategory) return;

    try {
      await deleteCategory(selectedCategory.id).unwrap();
      setDeleteDialogOpen(false);
      setSelectedCategory(null);
    } catch (error) {
      console.error("Error deleting category:", error);
    }
  };

  return (
    <div>
      <DataTable
        rows={categories}
        columns={categoryColumns}
        filterComponent={<CategoriesFilter />}
        actions={actions}
        onAdd={handleAdd}
      />

      <CategoryForm
        open={formOpen}
        onClose={() => setFormOpen(false)}
        onSubmit={handleFormSubmit}
        category={selectedCategory}
        isSubmitting={isCreating || isUpdating}
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Category"
        message={`Are you sure you want to delete the category "${selectedCategory?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        severity="error"
        isLoading={isDeleting}
      />
    </div>
  );
}

export default CategoriesPage;