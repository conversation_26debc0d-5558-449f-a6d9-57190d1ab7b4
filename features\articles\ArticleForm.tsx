"use client";

import { useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  TextField,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  FormHelperText,
  CircularProgress,
  Alert,
  Button,
} from "@mui/material";
import RichTextEditor from "@/components/shared/RichTextEditor/RichTextEditor";
import { Article } from "@/types";
import { useGetCategoriesQuery } from "@/lib/api/features/categories/categoriesApi";
import { useGetTagsQuery } from "@/lib/api/features/tags/tagsApi";
import { articleSchema, ArticleFormData } from "@/lib/schemas";

interface ArticleFormProps {
  onSubmit: (article: ArticleFormData) => void;
  onCancel?: () => void;
  article?: Article | null;
  isSubmitting?: boolean;
  submitText?: string;
  cancelText?: string;
}

export default function ArticleForm({
  onSubmit,
  onCancel,
  article,
  isSubmitting = false,
  submitText = "Submit",
  cancelText = "Cancel",
}: ArticleFormProps) {
  // RTK Query hooks
  const { data: categoriesResponse, isLoading: categoriesLoading } = useGetCategoriesQuery();
  const { data: tagsResponse, isLoading: tagsLoading } = useGetTagsQuery();

  // Extract data from responses
  const categories = categoriesResponse?.data || [];
  const tags = tagsResponse?.data || [];

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm<ArticleFormData>({
    resolver: yupResolver(articleSchema),
    mode: 'onChange', // Enable real-time validation
    defaultValues: {
      title: "",
      content: "",
      imageUrl: "",
      categoryId: 0, // Use 0 as default, will be validated as required
      tagIds: [],
    },
  });

  // Reset form when article changes
  useEffect(() => {
    if (article) {
      reset({
        title: article.title,
        content: article.content,
        imageUrl: article.imageUrl || "",
        categoryId: article.categoryId,
        tagIds: article.tags?.map(tag => tag.id) || [],
      });
    } else {
      reset({
        title: "",
        content: "",
        imageUrl: "",
        categoryId: 0, // Use 0 as default instead of empty string
        tagIds: [],
      });
    }
    clearErrors(); // ✅ clears validation errors
  }, [article, reset, clearErrors]);

  // Form submit handler
  const onFormSubmit = (data: ArticleFormData) => {
    onSubmit(data);
  };

  // Show loading state
  if (categoriesLoading || tagsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show error state
  // if (categoriesError || tagsError) {
  //   return (
  //     <Alert severity="error" sx={{ m: 2 }}>
  //       Failed to load form data. Please try again.
  //     </Alert>
  //   );
  // }

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(onFormSubmit)}
      sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}
    >
      {/* Title */}
      <Controller
        name="title"
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            label="Title"
            error={!!errors.title}
            helperText={errors.title?.message || "Article title (slug will be generated automatically)"}
            fullWidth
            required
            autoFocus
          />
        )}
      />

      {/* Content */}
      <Controller
        name="content"
        control={control}
        render={({ field }) => (
          <Box>
            <InputLabel sx={{ mb: 1, color: 'text.primary' }}>
              Content *
            </InputLabel>
            <RichTextEditor
              value={field.value}
              onChange={field.onChange}
              error={!!errors.content}
              helperText={errors.content?.message}
              placeholder="Write your article content here..."
              height={400}
            />
          </Box>
        )}
      />

      {/* Category */}
      <Controller
        name="categoryId"
        control={control}
        render={({ field }) => (
          <FormControl fullWidth required error={!!errors.categoryId}>
            <InputLabel>Category</InputLabel>
            <Select
              {...field}
              value={field.value || ""}
              label="Category"
            >
              {categories.map((category) => (
                <MenuItem key={category.id} value={category.id}>
                  {category.name}
                </MenuItem>
              ))}
            </Select>
            {errors.categoryId && (
              <FormHelperText>{errors.categoryId.message}</FormHelperText>
            )}
          </FormControl>
        )}
      />

      {/* Tags */}
      <Controller
        name="tagIds"
        control={control}
        render={({ field }) => (
          <FormControl fullWidth>
            <InputLabel>Tags</InputLabel>
            <Select
              {...field}
              multiple
              value={field.value ?? []}
              input={<OutlinedInput label="Tags" />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => {
                    const tag = tags.find(t => t.id === value);
                    return (
                      <Chip
                        key={value}
                        label={tag?.name}
                        size="small"
                      />
                    );
                  })}
                </Box>
              )}
            >
              {tags.map((tag) => (
                <MenuItem key={tag.id} value={tag.id}>
                  {tag.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
      />

      {/* Featured Image */}
      <Controller
        name="imageUrl"
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            label="Featured Image URL"
            error={!!errors.imageUrl}
            helperText={errors.imageUrl?.message || "Optional: URL to the featured image"}
            fullWidth
          />
        )}
      />

      {/* Action buttons */}
      <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
        {onCancel && (
          <Button
            variant="outlined"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {cancelText}
          </Button>
        )}
        <Button
          type="submit"
          variant="contained"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Submitting..." : submitText}
        </Button>
      </Box>
    </Box>
  );
}
