import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useDebounce } from "./useDebounce";
import { Route } from "next";

export function useUrlState(initialState: Record<string, string>) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();

  const [formState, setFormState] = useState(() => {
    const state = { ...initialState };
    Object.keys(state).forEach((key) => {
      state[key] = searchParams.get(key) || initialState[key];
    });
    return state;
  });

  const debouncedFormState = useDebounce(formState, 500);
  const prevStateRef = useRef(formState);

  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());

    const hasFilterChanges = Object.keys(debouncedFormState).some(
      (key) => debouncedFormState[key] !== prevStateRef.current[key]
    );

    Object.entries(debouncedFormState).forEach(([key, value]) => {
      if (value) params.set(key, value);
      else params.delete(key);
    });

    if (hasFilterChanges) {
      params.delete("offset");
    }

    prevStateRef.current = debouncedFormState;
    replace(`${pathname}?${params.toString()}` as Route);
  }, [debouncedFormState, pathname, replace, searchParams]);

  return [formState, setFormState] as const;
}
