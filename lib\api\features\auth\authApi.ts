import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { clearCredentials, setToken } from "../../slices/authSlice";

export const authApi = createApi({
  reducerPath: "authApi",
  baseQuery: fetchBaseQuery({ baseUrl: "/api" }),
  endpoints: (builder) => ({
    login: builder.mutation<{ accessToken: string, refreshToken: string }, { email: string; password: string }>({
      query: (body) => ({
        url: `http://localhost:3000/auth/login/`,
        method: "POST",
        body,
        credentials: "include",
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          dispatch(setToken(data.accessToken));
        } catch {}
      },
    }),
    refresh: builder.query<{ accessToken: string, refreshToken: string }, void>({
      query: () => ({
        url: "/refresh",
        method: "POST",
        credentials: "include",
      }),
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          dispatch(setToken(data.accessToken));
        } catch {
          dispatch(clearCredentials());
        }
      },
    }),
  }),
});

export const { useLoginMutation, useRefreshQuery } = authApi;
