// Database models matching Prisma schema

export interface Article {
  id: number;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  slug: string;
  likes: number;
  viewCount: number;
  imageUrl?: string;
  categoryId: number;
  category?: Category;
  tags?: Tag[];
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  createdAt: string;
  updatedAt: string;
  articles?: Article[];
}

export interface Tag {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  slug: string;
  articles?: Article[];
}

export interface Subscription {
  id: number;
  email: string;
  createdAt: string;
  updatedAt: string;
}

// Form data types are now exported from schemas
export type { ArticleFormData, CategoryFormData, TagFormData } from "@/lib/schemas";

export interface SubscriptionFormData {
  email: string;
}

// API response types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  offset: number;
  limit: number;
  totalPages: number;
}

// Filter types
export interface ArticleFilters {
  search?: string;
  categoryId?: number;
  tagId?: number;
  offset: number;
  limit: number;
}

export interface CategoryFilters {
  search?: string;
  offset: number;
  limit: number;
}

export interface TagFilters {
  search?: string;
  offset: number;
  limit: number;
}

export interface SubscriptionFilters {
  email?: string;
  offset: number;
  limit: number;
}

export interface Meta {
  total: number;
  offset: number;
  limit: number;
}

export interface ArticleResponse {
  data: Article[],
  meta: Meta
}

export interface CategoryResponse {
  data: Category[],
  meta: Meta
}

export interface TagResponse {
  data: Tag[],
  meta: Meta
}

export interface SubscriptionResponse {
  data: Subscription[],
  meta: Meta
}