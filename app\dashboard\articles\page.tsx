"use client";

import { useState, useMemo } from "react";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { Chip, Box, CircularProgress, Alert } from "@mui/material";
import DataTable, { Column, ActionButton } from "@/components/shared/DataTable";
import ArticlesFilter from "@/features/articles/ArticlesFilter";
import ConfirmDialog from "@/components/shared/ConfirmDialog";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { Article, ArticleFilters, CategoryFilters } from "@/types";
import { useGetArticlesQuery, useDeleteArticleMutation } from "@/lib/api/features/articles/articlesApi";
import { useGetCategoriesQuery } from "@/lib/api/features/categories/categoriesApi";
import { Route } from "next";

function ArticlesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const filters = useMemo((): ArticleFilters => {
    return {
      search: searchParams.get("search") || undefined,
      category: searchParams.get("category") ? parseInt(searchParams.get("category")!) : undefined,
      tag: searchParams.get("tag") ? parseInt(searchParams.get("tag")!) : undefined,
      offset: parseInt(searchParams.get("offset") || "0"),
      limit: parseInt(searchParams.get("limit") || "10"),
    };
  }, [searchParams]);

  const categoryFilters = useMemo((): CategoryFilters => {
    return {
      search: undefined,
      offset: 0,
      limit: 100, // Get all categories for filtering
    };
  }, []);

  // RTK Query hooks
  const { data: articlesResponse, isLoading: articlesLoading, error: articlesError } = useGetArticlesQuery(filters);
  const { data: categoriesResponse, isLoading: categoriesLoading } = useGetCategoriesQuery(categoryFilters);
  const [deleteArticle, { isLoading: isDeleting }] = useDeleteArticleMutation();

  // Extract data from responses
  const articles = articlesResponse?.data || [];
  const categories = categoriesResponse?.data || [];
  const meta = articlesResponse?.meta;

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);

  const articleColumns: Column[] = [
    { accessor: "title", label: "Title", minWidth: 200 },
    { accessor: "slug", label: "Slug", minWidth: 150 },
    { accessor: "category", label: "Category", minWidth: 120 },
    { accessor: "tags", label: "Tags", minWidth: 180 },
    { accessor: "viewCount", label: "Views", minWidth: 80 },
    { accessor: "createdAt", label: "Created At", minWidth: 150 },
    { accessor: "updatedAt", label: "Updated At", minWidth: 150 },
  ];

  // Show loading state
  if (articlesLoading || categoriesLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show error state
  if (articlesError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load articles. Please try again.
      </Alert>
    );
  }

  // Transform data for display
  const transformedArticles = articles.map(article => ({
    ...article,
    category: categories.find(c => c.id === article.category?.id)?.name || "Uncategorized",
    tags: (
      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
        {article.tags?.map(tag => (
          <Chip
            key={tag.id}
            label={tag.name}
            size="small"
            color="primary"
            variant="outlined"
          />
        ))}
      </Box>
    ),
  }));

  const actions: ActionButton[] = [
    {
      label: "Edit",
      icon: <EditIcon />,
      onClick: (article: Article) => {
        router.push(`/dashboard/articles/${article.id}/edit`);
      },
      color: "primary",
    },
    {
      label: "Delete",
      icon: <DeleteIcon />,
      onClick: (article: Article) => {
        setSelectedArticle(article);
        setDeleteDialogOpen(true);
      },
      color: "error",
    },
  ];

  const handleAdd = () => {
    router.push("/dashboard/articles/create");
  };

  const handleDelete = async () => {
    if (!selectedArticle) return;

    try {
      await deleteArticle(selectedArticle.id).unwrap();
      setDeleteDialogOpen(false);
      setSelectedArticle(null);
    } catch (error) {
      console.error("Error deleting article:", error);
    }
  };

  const handlePageChange = (offset: number, limit: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("offset", offset.toString());
    params.set("limit", limit.toString());

    router.push(`${pathname}?${params.toString()}` as Route);
  };

  return (
    <div>
      <DataTable
        rows={transformedArticles}
        columns={articleColumns}
        filterComponent={<ArticlesFilter />}
        actions={actions}
        onAdd={handleAdd}
        pagination={
          meta
            ? {
                offset: filters.offset,
                limit: filters.limit,
                total: meta.total,
                onPageChange: handlePageChange,
              }
            : undefined
        }
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        title="Delete Article"
        message={`Are you sure you want to delete the article "${selectedArticle?.title}"? This action cannot be undone.`}
        confirmText="Delete"
        severity="error"
        isLoading={isDeleting}
      />
    </div>
  );
}

export default ArticlesPage;