"use client";

import { useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { TextField, Box } from "@mui/material";
import FormDialog from "@/components/shared/FormDialog";
import { Tag } from "@/types";
import { tagSchema, TagFormData } from "@/lib/schemas";

interface TagFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (tag: TagFormData) => void;
  tag?: Tag | null;
  isSubmitting?: boolean;
}

export default function TagForm({
  open,
  onClose,
  onSubmit,
  tag,
  isSubmitting = false,
}: TagFormProps) {
  // React Hook Form setup
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<TagFormData>({
    resolver: yupResolver(tagSchema),
    defaultValues: {
      name: "",
    },
  });

  // Reset form when tag changes or dialog opens
  useEffect(() => {
    if (tag) {
      reset({
        name: tag.name,
      });
    } else {
      reset({
        name: "",
      });
    }
  }, [tag, open, reset]);

  // Form submit handler
  const onFormSubmit = (data: TagFormData) => {
    onSubmit(data);
  };

  const handleCancel = () => {
    reset({ name: "" });
    onClose();
  };

  return (
    <FormDialog
      open={open}
      onClose={onClose}
      title={tag ? "Edit Tag" : "Add Tag"}
      onSubmit={handleSubmit(onFormSubmit)}
      onCancel={handleCancel}
      isSubmitting={isSubmitting}
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              label="Name"
              error={!!errors.name}
              helperText={errors.name?.message || "Tag name (slug will be generated automatically)"}
              fullWidth
              required
              autoFocus
            />
          )}
        />
      </Box>
    </FormDialog>
  );
}
